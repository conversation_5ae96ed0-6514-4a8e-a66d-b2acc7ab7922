@echo off
:: System Cleanup Script - Enhanced CMD Version
:: Automatically runs as Administrator and cleans high resource usage processes, services, and startup programs
:: Double-click to run - will automatically request admin privileges

:: Check for admin privileges and auto-elevate if needed
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"
if '%errorlevel%' NEQ '0' (
    echo Requesting administrator privileges...
    goto UACPrompt
) else ( goto gotAdmin )

:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "", "", "runas", 1 >> "%temp%\getadmin.vbs"
    "%temp%\getadmin.vbs"
    del "%temp%\getadmin.vbs"
    exit /B

:gotAdmin
    pushd "%CD%"
    CD /D "%~dp0"

:: Initialize variables
set "LOGFILE=%USERPROFILE%\Desktop\SystemCleanup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt"
set "LOGFILE=%LOGFILE: =0%"
set "START_TIME=%time%"

:: Initialize log file
echo [%date% %time%] === System Cleanup Script Started === > "%LOGFILE%"
echo [%date% %time%] Script run by: %USERNAME% >> "%LOGFILE%"
echo [%date% %time%] Computer: %COMPUTERNAME% >> "%LOGFILE%"

echo.
echo ========================================
echo    SYSTEM CLEANUP SCRIPT - ENHANCED
echo ========================================
echo Script run by: %USERNAME%
echo Computer: %COMPUTERNAME%
echo Log file: %LOGFILE%
echo.

:: Check system resources using PowerShell for accuracy
echo [%date% %time%] Checking system resources... >> "%LOGFILE%"
echo Checking system resources...

powershell -Command "& {
    try {
        $RAM = Get-CimInstance -ClassName Win32_OperatingSystem;
        $TotalRAM = [math]::Round($RAM.TotalVisibleMemorySize / 1KB, 2);
        $FreeRAM = [math]::Round($RAM.FreePhysicalMemory / 1KB, 2);
        $UsedRAM = $TotalRAM - $FreeRAM;
        $RAMUsagePercent = [math]::Round(($UsedRAM / $TotalRAM) * 100, 2);
        $CPUUsage = (Get-Counter '\Processor(_Total)\%% Processor Time' -SampleInterval 1 -MaxSamples 3 | Select-Object -ExpandProperty CounterSamples | Measure-Object -Property CookedValue -Average).Average;
        $CPUUsagePercent = [math]::Round(100 - $CPUUsage, 2);
        Write-Output \"$RAMUsagePercent,$CPUUsagePercent,$TotalRAM,$UsedRAM\"
    } catch {
        Write-Output \"0,0,0,0\"
    }
}" > "%temp%\sysinfo.txt"

for /f "tokens=1,2,3,4 delims=," %%a in (%temp%\sysinfo.txt) do (
    set "RAM_USAGE=%%a"
    set "CPU_USAGE=%%b"
    set "TOTAL_RAM=%%c"
    set "USED_RAM=%%d"
)
del "%temp%\sysinfo.txt" 2>nul

echo [%date% %time%] Current RAM Usage: %RAM_USAGE%%% (%USED_RAM% GB / %TOTAL_RAM% GB) >> "%LOGFILE%"
echo [%date% %time%] Current CPU Usage: %CPU_USAGE%%% >> "%LOGFILE%"
echo Current RAM Usage: %RAM_USAGE%%% (%USED_RAM% GB / %TOTAL_RAM% GB)
echo Current CPU Usage: %CPU_USAGE%%%
echo.

:: Check if cleanup is needed (using PowerShell for decimal comparison)
powershell -Command "if ([double]'%RAM_USAGE%' -le 85 -and [double]'%CPU_USAGE%' -le 90) { exit 1 } else { exit 0 }" 2>nul
if %errorlevel% equ 1 (
    echo [%date% %time%] System resources are within acceptable limits. No cleanup needed. >> "%LOGFILE%"
    echo [%date% %time%] RAM: %RAM_USAGE%%% ^(threshold: 85%%%^) >> "%LOGFILE%"
    echo [%date% %time%] CPU: %CPU_USAGE%%% ^(threshold: 90%%%^) >> "%LOGFILE%"
    echo [%date% %time%] === Script Completed - No Action Taken === >> "%LOGFILE%"
    echo System resources are within acceptable limits. No cleanup needed.
    echo RAM: %RAM_USAGE%%% (threshold: 85%%)
    echo CPU: %CPU_USAGE%%% (threshold: 90%%)
    echo.
    echo === Script Completed - No Action Taken ===
    echo Press any key to exit...
    pause >nul
    exit /b 0
)

echo [%date% %time%] High resource usage detected. Proceeding with cleanup... >> "%LOGFILE%"
echo High resource usage detected. Proceeding with cleanup...
echo.

:: Phase 1: Kill high resource usage processes
echo ========================================
echo PHASE 1: KILLING HIGH RESOURCE PROCESSES
echo ========================================
echo [%date% %time%] === Phase 1: Killing High Resource Usage Processes === >> "%LOGFILE%"

set "KILLED_PROCESSES=0"

:: Use PowerShell to identify and kill high resource processes
powershell -Command "& {
    $SystemProcesses = @('System', 'smss', 'csrss', 'wininit', 'winlogon', 'services', 'lsass', 'lsm', 'svchost', 'explorer', 'dwm', 'audiodg', 'spoolsv', 'taskhost', 'taskhostw', 'RuntimeBroker', 'ApplicationFrameHost', 'ShellExperienceHost', 'SearchUI', 'StartMenuExperienceHost', 'cortana', 'SecurityHealthService', 'MsMpEng', 'NisSrv', 'WinDefend', 'wscsvc', 'WSearch', 'wuauclt', 'TrustedInstaller', 'fontdrvhost', 'conhost', 'dllhost', 'rundll32', 'msiexec', 'mscorsvw', 'WmiPrvSE', 'wmiprvse', 'unsecapp', 'WerFault', 'WerFaultSecure');
    $KilledCount = 0;
    try {
        $Processes = Get-Process | Where-Object { 
            $_.ProcessName -notin $SystemProcesses -and 
            $_.Path -notlike 'C:\Windows\System32\*' -and
            $_.Path -ne $null -and
            $_.ProcessName -ne 'powershell' -and
            $_.ProcessName -ne 'cmd'
        };
        foreach ($Process in $Processes) {
            try {
                $WorkingSet = $Process.WorkingSet64 / 1MB;
                $CPUTime = $Process.TotalProcessorTime.TotalSeconds;
                if ($WorkingSet -gt 100 -or $CPUTime -gt 30) {
                    if ($Process.Path) {
                        $FileInfo = Get-AuthenticodeSignature -FilePath $Process.Path -ErrorAction SilentlyContinue;
                        if ($FileInfo.SignerCertificate.Subject -like '*Microsoft*') {
                            Write-Output \"SKIP: $($Process.ProcessName) (PID: $($Process.Id)) - Microsoft signed\";
                            continue;
                        }
                    }
                    Stop-Process -Id $Process.Id -Force -ErrorAction SilentlyContinue;
                    Write-Output \"KILL: $($Process.ProcessName) (PID: $($Process.Id), RAM: $([math]::Round($WorkingSet, 2)) MB)\";
                    $KilledCount++;
                }
            } catch { }
        }
        Write-Output \"COUNT: $KilledCount\";
    } catch {
        Write-Output \"COUNT: 0\";
    }
}" > "%temp%\process_cleanup.txt"

for /f "tokens=1,* delims=:" %%a in (%temp%\process_cleanup.txt) do (
    if "%%a"=="KILL" (
        echo [%date% %time%] Killed process: %%b >> "%LOGFILE%"
        echo Killed process: %%b
        set /a KILLED_PROCESSES+=1
    ) else if "%%a"=="SKIP" (
        echo [%date% %time%] Skipped Microsoft signed process: %%b >> "%LOGFILE%"
    ) else if "%%a"=="COUNT" (
        set "KILLED_PROCESSES=%%b"
    )
)
del "%temp%\process_cleanup.txt" 2>nul

echo [%date% %time%] Total processes killed: %KILLED_PROCESSES% >> "%LOGFILE%"
echo Total processes killed: %KILLED_PROCESSES%
echo.

:: Phase 2: Stop non-Microsoft services
echo ========================================
echo PHASE 2: STOPPING NON-MICROSOFT SERVICES
echo ========================================
echo [%date% %time%] === Phase 2: Stopping Non-Microsoft Services === >> "%LOGFILE%"

set "STOPPED_SERVICES=0"

:: Use PowerShell to identify and stop non-Microsoft services
powershell -Command "& {
    $StoppedCount = 0;
    try {
        $Services = Get-Service | Where-Object {
            $_.Status -eq 'Running' -and
            $_.ServiceName -notlike 'Win*' -and
            $_.ServiceName -notlike 'Microsoft*' -and
            $_.ServiceName -notlike 'MS*' -and
            $_.ServiceName -notlike 'WSearch*' -and
            $_.ServiceName -notlike 'Defender*' -and
            $_.ServiceName -notlike 'Security*'
        };
        foreach ($Service in $Services) {
            try {
                $ServiceDetails = Get-CimInstance -ClassName Win32_Service -Filter \"Name='$($Service.ServiceName)'\";
                if ($ServiceDetails.PathName) {
                    $ServicePath = $ServiceDetails.PathName -replace '\"', '' -split ' ' | Select-Object -First 1;
                    if ($ServicePath -like 'C:\Windows\System32\*' -or $ServicePath -like 'C:\Windows\SysWOW64\*') {
                        continue;
                    }
                    if (Test-Path $ServicePath) {
                        $FileInfo = Get-AuthenticodeSignature -FilePath $ServicePath -ErrorAction SilentlyContinue;
                        if ($FileInfo.SignerCertificate.Subject -like '*Microsoft*') {
                            Write-Output \"SKIP: $($Service.ServiceName) - Microsoft signed\";
                            continue;
                        }
                    }
                }
                Stop-Service -Name $Service.ServiceName -Force -ErrorAction SilentlyContinue;
                Write-Output \"STOP: $($Service.ServiceName) - $($Service.DisplayName)\";
                $StoppedCount++;
            } catch { }
        }
        Write-Output \"COUNT: $StoppedCount\";
    } catch {
        Write-Output \"COUNT: 0\";
    }
}" > "%temp%\service_cleanup.txt"

for /f "tokens=1,* delims=:" %%a in (%temp%\service_cleanup.txt) do (
    if "%%a"=="STOP" (
        echo [%date% %time%] Stopped service: %%b >> "%LOGFILE%"
        echo Stopped service: %%b
        set /a STOPPED_SERVICES+=1
    ) else if "%%a"=="SKIP" (
        echo [%date% %time%] Skipped Microsoft signed service: %%b >> "%LOGFILE%"
    ) else if "%%a"=="COUNT" (
        set "STOPPED_SERVICES=%%b"
    )
)
del "%temp%\service_cleanup.txt" 2>nul

echo [%date% %time%] Total services stopped: %STOPPED_SERVICES% >> "%LOGFILE%"
echo Total services stopped: %STOPPED_SERVICES%
echo.

:: Phase 3: Disable startup programs
echo ========================================
echo PHASE 3: DISABLING STARTUP PROGRAMS
echo ========================================
echo [%date% %time%] === Phase 3: Disabling Startup Programs === >> "%LOGFILE%"

set "DISABLED_STARTUPS=0"

:: Clean HKCU Run registry
echo Cleaning HKCU startup entries...
for /f "tokens=1,2,*" %%a in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" 2^>nul ^| findstr "REG_"') do (
    reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "%%a" /f >nul 2>&1
    if !errorlevel! equ 0 (
        echo [%date% %time%] Removed HKCU startup entry: %%a = %%c >> "%LOGFILE%"
        echo Removed HKCU startup entry: %%a
        set /a DISABLED_STARTUPS+=1
    )
)

:: Clean HKLM Run registry (with safety checks)
echo Cleaning HKLM startup entries...
powershell -Command "& {
    $DisabledCount = 0;
    try {
        $HKLMPath = 'HKLM:\Software\Microsoft\Windows\CurrentVersion\Run';
        if (Test-Path $HKLMPath) {
            $HKLMEntries = Get-ItemProperty -Path $HKLMPath -ErrorAction SilentlyContinue;
            foreach ($Property in $HKLMEntries.PSObject.Properties) {
                if ($Property.Name -notin @('PSPath', 'PSParentPath', 'PSChildName', 'PSDrive', 'PSProvider')) {
                    $Value = $Property.Value;
                    if ($Value -like '*Windows*' -or $Value -like '*Microsoft*' -or $Value -like '*System32*') {
                        Write-Output \"SKIP: $($Property.Name) - Windows/Microsoft component\";
                        continue;
                    }
                    try {
                        Remove-ItemProperty -Path $HKLMPath -Name $Property.Name -ErrorAction SilentlyContinue;
                        Write-Output \"REMOVE: $($Property.Name) = $($Property.Value)\";
                        $DisabledCount++;
                    } catch { }
                }
            }
        }
        Write-Output \"COUNT: $DisabledCount\";
    } catch {
        Write-Output \"COUNT: 0\";
    }
}" > "%temp%\hklm_cleanup.txt"

for /f "tokens=1,* delims=:" %%a in (%temp%\hklm_cleanup.txt) do (
    if "%%a"=="REMOVE" (
        echo [%date% %time%] Removed HKLM startup entry: %%b >> "%LOGFILE%"
        echo Removed HKLM startup entry: %%b
        set /a DISABLED_STARTUPS+=1
    ) else if "%%a"=="SKIP" (
        echo [%date% %time%] Skipped Windows/Microsoft startup entry: %%b >> "%LOGFILE%"
    ) else if "%%a"=="COUNT" (
        set /a DISABLED_STARTUPS+=%%b
    )
)
del "%temp%\hklm_cleanup.txt" 2>nul

:: Disable non-Microsoft scheduled tasks
echo Disabling non-Microsoft scheduled tasks...
powershell -Command "& {
    $DisabledCount = 0;
    try {
        $ScheduledTasks = Get-ScheduledTask | Where-Object {
            $_.State -eq 'Ready' -and
            $_.TaskPath -notlike '*Microsoft*' -and
            $_.TaskPath -notlike '*Windows*' -and
            $_.Author -notlike '*Microsoft*'
        };
        foreach ($Task in $ScheduledTasks) {
            try {
                Disable-ScheduledTask -TaskName $Task.TaskName -TaskPath $Task.TaskPath -ErrorAction SilentlyContinue;
                Write-Output \"DISABLE: $($Task.TaskPath)$($Task.TaskName)\";
                $DisabledCount++;
            } catch { }
        }
        Write-Output \"COUNT: $DisabledCount\";
    } catch {
        Write-Output \"COUNT: 0\";
    }
}" > "%temp%\task_cleanup.txt"

for /f "tokens=1,* delims=:" %%a in (%temp%\task_cleanup.txt) do (
    if "%%a"=="DISABLE" (
        echo [%date% %time%] Disabled scheduled task: %%b >> "%LOGFILE%"
        echo Disabled scheduled task: %%b
        set /a DISABLED_STARTUPS+=1
    ) else if "%%a"=="COUNT" (
        set /a DISABLED_STARTUPS+=%%b
    )
)
del "%temp%\task_cleanup.txt" 2>nul

echo [%date% %time%] Total startup items disabled: %DISABLED_STARTUPS% >> "%LOGFILE%"
echo Total startup items disabled: %DISABLED_STARTUPS%
echo.

:: Final resource check
echo ========================================
echo FINAL SYSTEM RESOURCE CHECK
echo ========================================
echo [%date% %time%] === Final System Resource Check === >> "%LOGFILE%"

echo Checking final system resources...

powershell -Command "& {
    try {
        $RAM = Get-CimInstance -ClassName Win32_OperatingSystem;
        $TotalRAM = [math]::Round($RAM.TotalVisibleMemorySize / 1KB, 2);
        $FreeRAM = [math]::Round($RAM.FreePhysicalMemory / 1KB, 2);
        $UsedRAM = $TotalRAM - $FreeRAM;
        $RAMUsagePercent = [math]::Round(($UsedRAM / $TotalRAM) * 100, 2);
        $CPUUsage = (Get-Counter '\Processor(_Total)\%% Processor Time' -SampleInterval 1 -MaxSamples 3 | Select-Object -ExpandProperty CounterSamples | Measure-Object -Property CookedValue -Average).Average;
        $CPUUsagePercent = [math]::Round(100 - $CPUUsage, 2);
        Write-Output \"$RAMUsagePercent,$CPUUsagePercent,$TotalRAM,$UsedRAM\"
    } catch {
        Write-Output \"0,0,0,0\"
    }
}" > "%temp%\final_sysinfo.txt"

for /f "tokens=1,2,3,4 delims=," %%a in (%temp%\final_sysinfo.txt) do (
    set "FINAL_RAM_USAGE=%%a"
    set "FINAL_CPU_USAGE=%%b"
    set "FINAL_TOTAL_RAM=%%c"
    set "FINAL_USED_RAM=%%d"
)
del "%temp%\final_sysinfo.txt" 2>nul

echo [%date% %time%] Final RAM Usage: %FINAL_RAM_USAGE%%% (%FINAL_USED_RAM% GB / %FINAL_TOTAL_RAM% GB) >> "%LOGFILE%"
echo [%date% %time%] Final CPU Usage: %FINAL_CPU_USAGE%%% >> "%LOGFILE%"
echo Final RAM Usage: %FINAL_RAM_USAGE%%% (%FINAL_USED_RAM% GB / %FINAL_TOTAL_RAM% GB)
echo Final CPU Usage: %FINAL_CPU_USAGE%%%

:: Calculate improvements using PowerShell
powershell -Command "& {
    try {
        $RAMImprovement = [double]'%RAM_USAGE%' - [double]'%FINAL_RAM_USAGE%';
        $CPUImprovement = [double]'%CPU_USAGE%' - [double]'%FINAL_CPU_USAGE%';
        Write-Output \"$([math]::Round($RAMImprovement, 2)),$([math]::Round($CPUImprovement, 2))\"
    } catch {
        Write-Output \"0,0\"
    }
}" > "%temp%\improvements.txt"

for /f "tokens=1,2 delims=," %%a in (%temp%\improvements.txt) do (
    set "RAM_IMPROVEMENT=%%a"
    set "CPU_IMPROVEMENT=%%b"
)
del "%temp%\improvements.txt" 2>nul

echo [%date% %time%] RAM Usage Improvement: %RAM_IMPROVEMENT%%% >> "%LOGFILE%"
echo [%date% %time%] CPU Usage Improvement: %CPU_IMPROVEMENT%%% >> "%LOGFILE%"
echo RAM Usage Improvement: %RAM_IMPROVEMENT%%%
echo CPU Usage Improvement: %CPU_IMPROVEMENT%%%
echo.

:: Calculate execution time
set "END_TIME=%time%"
powershell -Command "& {
    try {
        $StartTime = [datetime]'%START_TIME%';
        $EndTime = [datetime]'%END_TIME%';
        $Duration = $EndTime - $StartTime;
        Write-Output $Duration.TotalSeconds
    } catch {
        Write-Output '0'
    }
}" > "%temp%\duration.txt"

for /f %%a in (%temp%\duration.txt) do set "DURATION=%%a"
del "%temp%\duration.txt" 2>nul

echo [%date% %time%] Script execution time: %DURATION% seconds >> "%LOGFILE%"
echo Script execution time: %DURATION% seconds

:: Force garbage collection
powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers()" 2>nul

echo [%date% %time%] === System Cleanup Script Completed === >> "%LOGFILE%"
echo [%date% %time%] Log file saved to: %LOGFILE% >> "%LOGFILE%"

echo.
echo ========================================
echo    CLEANUP COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo SUMMARY:
echo - Processes killed: %KILLED_PROCESSES%
echo - Services stopped: %STOPPED_SERVICES%
echo - Startup items disabled: %DISABLED_STARTUPS%
echo - RAM improvement: %RAM_IMPROVEMENT%%%
echo - CPU improvement: %CPU_IMPROVEMENT%%%
echo - Execution time: %DURATION% seconds
echo.
echo Log file saved to: %LOGFILE%
echo.
echo Press any key to exit...
pause >nul

:: Cleanup temp files
del "%temp%\*.txt" 2>nul

exit /b 0
