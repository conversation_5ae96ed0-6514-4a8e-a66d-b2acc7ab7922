# System Cleanup Script
# Automatically cleans high resource usage processes, services, and startup programs
# Run as Administrator

# Set execution policy for this session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Initialize log file
$LogFile = "$env:USERPROFILE\Desktop\SystemCleanup_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
$StartTime = Get-Date

function Write-Log {
    param([string]$Message)
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "[$Timestamp] $Message"
    Write-Host $LogEntry
    Add-Content -Path $LogFile -Value $LogEntry
}

function Get-SystemResources {
    $RAM = Get-CimInstance -ClassName Win32_OperatingSystem
    $CPU = Get-CimInstance -ClassName Win32_Processor
    
    $TotalRAM = [math]::Round($RAM.TotalVisibleMemorySize / 1KB, 2)
    $FreeRAM = [math]::Round($RAM.FreePhysicalMemory / 1KB, 2)
    $UsedRAM = $TotalRAM - $FreeRAM
    $RAMUsagePercent = [math]::Round(($UsedRAM / $TotalRAM) * 100, 2)
    
    # Get CPU usage over 5 seconds
    $CPUUsage = (Get-Counter "\Processor(_Total)\% Processor Time" -SampleInterval 1 -MaxSamples 5 | 
                 Select-Object -ExpandProperty CounterSamples | 
                 Measure-Object -Property CookedValue -Average).Average
    $CPUUsagePercent = [math]::Round(100 - $CPUUsage, 2)
    
    return @{
        RAMUsage = $RAMUsagePercent
        CPUUsage = $CPUUsagePercent
        TotalRAM = $TotalRAM
        UsedRAM = $UsedRAM
    }
}

# Core Windows processes that should never be killed
$SystemProcesses = @(
    'System', 'smss', 'csrss', 'wininit', 'winlogon', 'services', 'lsass', 'lsm',
    'svchost', 'explorer', 'dwm', 'audiodg', 'spoolsv', 'taskhost', 'taskhostw',
    'RuntimeBroker', 'ApplicationFrameHost', 'ShellExperienceHost', 'SearchUI',
    'StartMenuExperienceHost', 'cortana', 'SecurityHealthService', 'MsMpEng',
    'NisSrv', 'WinDefend', 'wscsvc', 'WSearch', 'wuauclt', 'TrustedInstaller',
    'fontdrvhost', 'conhost', 'dllhost', 'rundll32', 'msiexec', 'mscorsvw',
    'WmiPrvSE', 'wmiprvse', 'unsecapp', 'WerFault', 'WerFaultSecure'
)

Write-Log "=== System Cleanup Script Started ==="
Write-Log "Script run by: $env:USERNAME"
Write-Log "Computer: $env:COMPUTERNAME"

# Check system resources
Write-Log "Checking system resources..."
$Resources = Get-SystemResources
Write-Log "Current RAM Usage: $($Resources.RAMUsage)% ($($Resources.UsedRAM) GB / $($Resources.TotalRAM) GB)"
Write-Log "Current CPU Usage: $($Resources.CPUUsage)%"

# Check if cleanup is needed
if ($Resources.RAMUsage -le 85 -and $Resources.CPUUsage -le 90) {
    Write-Log "System resources are within acceptable limits. No cleanup needed."
    Write-Log "RAM: $($Resources.RAMUsage)% (threshold: 85%)"
    Write-Log "CPU: $($Resources.CPUUsage)% (threshold: 90%)"
    Write-Log "=== Script Completed - No Action Taken ==="
    exit 0
}

Write-Log "High resource usage detected. Proceeding with cleanup..."

# Kill high resource usage processes
Write-Log "=== Phase 1: Killing High Resource Usage Processes ==="

try {
    $Processes = Get-Process | Where-Object { 
        $_.ProcessName -notin $SystemProcesses -and 
        $_.Path -notlike "C:\Windows\System32\*" -and
        $_.Path -ne $null
    }
    
    $KilledProcesses = 0
    
    foreach ($Process in $Processes) {
        try {
            # Check if process is using high CPU or memory
            $CPUTime = $Process.TotalProcessorTime.TotalSeconds
            $WorkingSet = $Process.WorkingSet64 / 1MB
            
            # Kill if using more than 100MB RAM or appears to be high CPU usage
            if ($WorkingSet -gt 100 -or $CPUTime -gt 30) {
                $ProcessInfo = "$($Process.ProcessName) (PID: $($Process.Id), RAM: $([math]::Round($WorkingSet, 2)) MB)"
                
                # Additional safety check - don't kill if it's a Microsoft signed process
                if ($Process.Path) {
                    $FileInfo = Get-AuthenticodeSignature -FilePath $Process.Path -ErrorAction SilentlyContinue
                    if ($FileInfo.SignerCertificate.Subject -like "*Microsoft*") {
                        Write-Log "Skipping Microsoft signed process: $ProcessInfo"
                        continue
                    }
                }
                
                Stop-Process -Id $Process.Id -Force -ErrorAction SilentlyContinue
                Write-Log "Killed process: $ProcessInfo"
                $KilledProcesses++
            }
        }
        catch {
            Write-Log "Failed to kill process $($Process.ProcessName): $($_.Exception.Message)"
        }
    }
    
    Write-Log "Total processes killed: $KilledProcesses"
}
catch {
    Write-Log "Error in process cleanup: $($_.Exception.Message)"
}

# Stop non-Microsoft services
Write-Log "=== Phase 2: Stopping Non-Microsoft Services ==="

try {
    $Services = Get-Service | Where-Object { 
        $_.Status -eq 'Running' -and 
        $_.ServiceName -notlike "Win*" -and
        $_.ServiceName -notlike "Microsoft*" -and
        $_.ServiceName -notlike "MS*" -and
        $_.ServiceName -notlike "WSearch*" -and
        $_.ServiceName -notlike "Defender*" -and
        $_.ServiceName -notlike "Security*"
    }
    
    $StoppedServices = 0
    
    foreach ($Service in $Services) {
        try {
            # Additional check - verify it's not a Microsoft service by checking the executable path
            $ServiceDetails = Get-CimInstance -ClassName Win32_Service -Filter "Name='$($Service.ServiceName)'"
            
            if ($ServiceDetails.PathName) {
                $ServicePath = $ServiceDetails.PathName -replace '"', '' -split ' ' | Select-Object -First 1
                
                # Skip if service is in System32 or signed by Microsoft
                if ($ServicePath -like "C:\Windows\System32\*" -or $ServicePath -like "C:\Windows\SysWOW64\*") {
                    continue
                }
                
                if (Test-Path $ServicePath) {
                    $FileInfo = Get-AuthenticodeSignature -FilePath $ServicePath -ErrorAction SilentlyContinue
                    if ($FileInfo.SignerCertificate.Subject -like "*Microsoft*") {
                        Write-Log "Skipping Microsoft signed service: $($Service.ServiceName)"
                        continue
                    }
                }
            }
            
            Stop-Service -Name $Service.ServiceName -Force -ErrorAction SilentlyContinue
            Write-Log "Stopped service: $($Service.ServiceName) - $($Service.DisplayName)"
            $StoppedServices++
        }
        catch {
            Write-Log "Failed to stop service $($Service.ServiceName): $($_.Exception.Message)"
        }
    }
    
    Write-Log "Total services stopped: $StoppedServices"
}
catch {
    Write-Log "Error in service cleanup: $($_.Exception.Message)"
}

# Disable startup programs
Write-Log "=== Phase 3: Disabling Startup Programs ==="

$DisabledStartups = 0

# Remove from HKCU Run registry
try {
    $HKCUPath = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run"
    if (Test-Path $HKCUPath) {
        $HKCUEntries = Get-ItemProperty -Path $HKCUPath -ErrorAction SilentlyContinue
        foreach ($Property in $HKCUEntries.PSObject.Properties) {
            if ($Property.Name -notin @('PSPath', 'PSParentPath', 'PSChildName', 'PSDrive', 'PSProvider')) {
                try {
                    Remove-ItemProperty -Path $HKCUPath -Name $Property.Name -ErrorAction SilentlyContinue
                    Write-Log "Removed HKCU startup entry: $($Property.Name) = $($Property.Value)"
                    $DisabledStartups++
                }
                catch {
                    Write-Log "Failed to remove HKCU startup entry $($Property.Name): $($_.Exception.Message)"
                }
            }
        }
    }
}
catch {
    Write-Log "Error accessing HKCU Run registry: $($_.Exception.Message)"
}

# Remove from HKLM Run registry
try {
    $HKLMPath = "HKLM:\Software\Microsoft\Windows\CurrentVersion\Run"
    if (Test-Path $HKLMPath) {
        $HKLMEntries = Get-ItemProperty -Path $HKLMPath -ErrorAction SilentlyContinue
        foreach ($Property in $HKLMEntries.PSObject.Properties) {
            if ($Property.Name -notin @('PSPath', 'PSParentPath', 'PSChildName', 'PSDrive', 'PSProvider')) {
                # Check if it's a Microsoft/Windows component
                $Value = $Property.Value
                if ($Value -like "*Windows*" -or $Value -like "*Microsoft*" -or $Value -like "*System32*") {
                    Write-Log "Skipping Windows/Microsoft startup entry: $($Property.Name)"
                    continue
                }
                
                try {
                    Remove-ItemProperty -Path $HKLMPath -Name $Property.Name -ErrorAction SilentlyContinue
                    Write-Log "Removed HKLM startup entry: $($Property.Name) = $($Property.Value)"
                    $DisabledStartups++
                }
                catch {
                    Write-Log "Failed to remove HKLM startup entry $($Property.Name): $($_.Exception.Message)"
                }
            }
        }
    }
}
catch {
    Write-Log "Error accessing HKLM Run registry: $($_.Exception.Message)"
}

# Disable non-Microsoft scheduled tasks
try {
    $ScheduledTasks = Get-ScheduledTask | Where-Object { 
        $_.State -eq 'Ready' -and 
        $_.TaskPath -notlike "*Microsoft*" -and
        $_.TaskPath -notlike "*Windows*" -and
        $_.Author -notlike "*Microsoft*"
    }
    
    foreach ($Task in $ScheduledTasks) {
        try {
            Disable-ScheduledTask -TaskName $Task.TaskName -TaskPath $Task.TaskPath -ErrorAction SilentlyContinue
            Write-Log "Disabled scheduled task: $($Task.TaskPath)$($Task.TaskName)"
            $DisabledStartups++
        }
        catch {
            Write-Log "Failed to disable scheduled task $($Task.TaskName): $($_.Exception.Message)"
        }
    }
}
catch {
    Write-Log "Error disabling scheduled tasks: $($_.Exception.Message)"
}

Write-Log "Total startup items disabled: $DisabledStartups"

# Final resource check
Write-Log "=== Final System Resource Check ==="
$FinalResources = Get-SystemResources
Write-Log "Final RAM Usage: $($FinalResources.RAMUsage)% ($($FinalResources.UsedRAM) GB / $($FinalResources.TotalRAM) GB)"
Write-Log "Final CPU Usage: $($FinalResources.CPUUsage)%"

$RAMImprovement = $Resources.RAMUsage - $FinalResources.RAMUsage
$CPUImprovement = $Resources.CPUUsage - $FinalResources.CPUUsage

Write-Log "RAM Usage Improvement: $([math]::Round($RAMImprovement, 2))%"
Write-Log "CPU Usage Improvement: $([math]::Round($CPUImprovement, 2))%"

$EndTime = Get-Date
$Duration = $EndTime - $StartTime
Write-Log "Script execution time: $($Duration.TotalSeconds) seconds"

Write-Log "=== System Cleanup Script Completed ==="
Write-Log "Log file saved to: $LogFile"

# Force garbage collection to free up memory
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

Write-Host "`nCleanup completed! Check the log file on your desktop for details."
Write-Host "Log file: $LogFile" 